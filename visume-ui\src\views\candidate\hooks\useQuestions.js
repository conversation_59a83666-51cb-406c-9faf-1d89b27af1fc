import { useReducer, useEffect, useCallback, useRef } from "react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

// Default questions for fallback
const getDefaultQuestion = (role, skill, type = 'coding') => {
  const questions = {
    coding: {
      question: `Describe a challenging problem you've solved using ${skill}. What was your approach and solution?`,
      type: 'coding'
    },
    verbal: {
      question: `How do you handle difficult situations in your role as a ${role}? Give a specific example.`,
      type: 'verbal'
    }
  };
  return questions[type] || questions.coding;
};


const ACTIONS = {
  SET_QUESTION_META: 'SET_QUESTION_META',
  SET_QUESTIONS: 'SET_QUESTIONS',
  SET_CURRENT_QUESTION: 'SET_CURRENT_QUESTION',
  NEXT_QUESTION: 'NEXT_QUESTION',
  UPDATE_ANSWER: 'UPDATE_ANSWER',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_TRANSITIONING: 'SET_TRANSITIONING',
  SET_QUESTIONS_LOADED: 'SET_QUESTIONS_LOADED',
  SET_HAS_NARRATED: 'SET_HAS_NARRATED',
  SET_TRANSCRIBING: 'SET_TRANSCRIBING',
  SET_LAST_SAVED_ANSWER: 'SET_LAST_SAVED_ANSWER',
  SET_AUDIO_INSTANCE: 'SET_AUDIO_INSTANCE',
  SET_ANSWER_TIMER: 'SET_ANSWER_TIMER',
  SET_TIME_UP: 'SET_TIME_UP'
};

// Initial state
const initialState = {
  allQuestions: [],
  currentIndex: 0,
  currentQuestion: null,
  error: null,
  isLoading: false,
  questionsLoaded: false,
  isTransitioning: false,
  hasNarrated: false,
  isTranscribing: false, // Track transcription status
  lastSavedAnswer: null, // Track last successfully saved answer
  audioInstance: null, // Track current audio instance
  answerTimer: null, // Track answer time limit
  isTimeUp: false // Track if answer time is up
};

// Reducer function
const questionsReducer = (state, action) => {
  // Prevent duplicate question additions by validating state
  if (action.type === ACTIONS.NEXT_QUESTION &&
      state.allQuestions.some(q =>
        q.question === action.payload.nextQuestionData.question)) {
    console.warn("Duplicate question detected, skipping addition");
    return state;
  }

  switch (action.type) {
    case ACTIONS.SET_AUDIO_INSTANCE:
      if (state.audioInstance) {
        state.audioInstance.pause();
        state.audioInstance.cancel();
      }
      return {
        ...state,
        audioInstance: action.instance
      };
     
    case ACTIONS.SET_ANSWER_TIMER:
      if (state.answerTimer) {
        clearTimeout(state.answerTimer);
      }
      return {
        ...state,
        answerTimer: action.timer,
        isTimeUp: false
      };

    case ACTIONS.SET_TIME_UP:
      return {
        ...state,
        isTimeUp: action.isTimeUp
      };

    case ACTIONS.SET_QUESTIONS:
      return {
        ...state,
        allQuestions: action.questions,
        currentIndex: action.index || 0,
        currentQuestion: action.questions[action.index || 0] || null
      };
    
    case ACTIONS.NEXT_QUESTION:
      const updatedQuestions = action.payload.updatedQuestions;
      const nextIndex = state.currentIndex + 1;
      return {
        ...state,
        allQuestions: updatedQuestions,
        currentIndex: nextIndex,
        currentQuestion: action.payload.nextQuestionData,
        isTransitioning: false,
        isLoading: false
      };

    case ACTIONS.SET_QUESTION_META:
          const updatedQuestionWithMeta = {
            ...state.currentQuestion,
            ...action.payload
          };
          const questionsWithMeta = [...state.allQuestions];
          questionsWithMeta[state.currentIndex] = updatedQuestionWithMeta;
          return {
            ...state,
            allQuestions: questionsWithMeta,
            currentQuestion: updatedQuestionWithMeta
          };
    
    case ACTIONS.UPDATE_ANSWER:
          // Never accept null/undefined answers
          const answer = action.payload || '';
          console.log('Updating answer:', { answer, questionIndex: state.currentIndex });
          
          const questionsWithAnswer = [...state.allQuestions];
          questionsWithAnswer[state.currentIndex] = {
            ...state.currentQuestion,
            answer,
            lastModified: new Date().toISOString(),
            status: 'answered',
            isComplete: true
          };
          
          return {
            ...state,
            allQuestions: questionsWithAnswer,
            currentQuestion: {
              ...state.currentQuestion,
              answer,
              lastModified: new Date().toISOString(),
              status: 'answered',
              isComplete: true
            }
          };

    case ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.isLoading
      };

    case ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.error
      };

    case ACTIONS.SET_TRANSITIONING:
      return {
        ...state,
        isTransitioning: action.isTransitioning
      };

    case ACTIONS.SET_QUESTIONS_LOADED:
      return {
        ...state,
        questionsLoaded: action.loaded
      };

    case ACTIONS.SET_HAS_NARRATED:
      return {
        ...state,
        hasNarrated: action.hasNarrated
      };

    case ACTIONS.SET_TRANSCRIBING:
      return {
        ...state,
        isTranscribing: action.isTranscribing
      };

    case ACTIONS.SET_LAST_SAVED_ANSWER:
      return {
        ...state,
        lastSavedAnswer: action.answer
      };

    default:
      return state;
  }
};

export function useQuestions(isInterviewActive = false, vpid = null) {
  const [state, dispatch] = useReducer(questionsReducer, initialState);
  const isInitialMount = useRef(true);
  const allQuestionsRef = useRef(state.allQuestions); // Ref to hold the latest allQuestions
  const ANSWER_TIME_LIMIT = 120000; // 2 minutes in milliseconds
  
  // Cleanup function for audio and timers
  const cleanup = () => {
    if (state.audioInstance) {
      state.audioInstance.pause();
      state.audioInstance.cancel();
    }
    if (state.answerTimer) {
      clearTimeout(state.answerTimer);
    }
    window.speechSynthesis.cancel();
  };

  // Handle cleanup on unmount
  useEffect(() => {
    return () => cleanup();
  }, []);

  // Keep the ref updated with the latest allQuestions state
  useEffect(() => {
    allQuestionsRef.current = state.allQuestions;
  }, [state.allQuestions]);


  // Validate question state
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (state.allQuestions.length > 0 && 
        state.currentIndex >= 0 && 
        state.currentIndex < state.allQuestions.length) {
      const question = state.allQuestions[state.currentIndex];
      if (!state.currentQuestion || 
          JSON.stringify(question) !== JSON.stringify(state.currentQuestion)) {
        dispatch({
          type: ACTIONS.SET_QUESTIONS,
          questions: state.allQuestions,
          index: state.currentIndex
        });
      }
    } else if (!isInitialMount.current && !state.isLoading) {
      console.warn("Invalid questions state:", {
        questionsLength: state.allQuestions.length,
        currentIndex: state.currentIndex,
        isLoading: state.isLoading,
        currentQuestion: state.currentQuestion?.question || null
      });
    }
  }, [state.allQuestions, state.currentIndex]);

  const loadQuestionsData = async () => {
    try {
      dispatch({ type: ACTIONS.SET_LOADING, isLoading: true });
      
      const createVRResJson = Cookies.get("CreateVRres");
      const parsedData = JSON.parse(createVRResJson || localStorage.getItem("interviewData"));
      const skills = parsedData?.skills || ["JavaScript"];
      const role = parsedData?.jobRole || "Software Engineer";

      // Validate and sanitize input parameters
      if (!role || !Array.isArray(skills) || skills.length === 0) {
        throw new Error('Invalid role or skills configuration');
      }
      
      // Log only the questions state
      console.log("Questions state:", state.allQuestions);
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role: role.trim(),
          skills: skills.map(s => s.trim()).filter(Boolean),
          totalQuestions: 5,
          batchSize: 5,
          previousQuestions: state.allQuestions.map(q => q.question)
        })
      });

      console.log(
        "Sending question generation payload:",
        JSON.stringify({
          role: role.trim(),
          skills: skills.map(s => s.trim()).filter(Boolean),
          totalQuestions: 5,
          batchSize: 5,
          previousQuestions: state.allQuestions.map(q => q.question)
        })
      );
      const data = await response.json();
      if (!data.questions || !Array.isArray(data.questions)) {
        throw new Error('Invalid response format - expected array of questions');
      }

      const initialQuestions = data.questions.map((q, index) => ({
        question: q.question,
        type: q.type || 'coding',
        timerDuration: q.timerDuration || 90, // Add timer duration with fallback
        startTimestamp: null,
        endTimestamp: null,
        answer: q.answer || '', // Use empty string instead of null
        follow_up: q.follow_up || null,
        status: 'pending',
        isComplete: false,
        questionNumber: index + 1,
        scores: {
          communication: 0,
          technical: 0,
          overall: 0
        }
      }));

      dispatch({
        type: ACTIONS.SET_QUESTIONS,
        questions: initialQuestions,
        index: 0
      });
      dispatch({ type: ACTIONS.SET_QUESTIONS_LOADED, loaded: true });
      
    } catch (error) {
      console.error("Error loading questions:", error);
      dispatch({ type: ACTIONS.SET_ERROR, error: "Failed to load questions" });
    } finally {
      dispatch({ type: ACTIONS.SET_LOADING, isLoading: false });
    }
  };

  const nextQuestion = async () => {
    // Prevent fetching if interview is not active
    if (!isInterviewActive) {
      toast.error("Interview has ended. No further questions can be fetched.");
      return false;
    }

    cleanup(); // Clean up audio and timers

    try {
      // Only check transcription status
      if (state.currentQuestion && state.isTranscribing) {
        console.log("Waiting for answer transcription to complete");
        toast.error("Please wait for your answer to be recorded");
        return false;
      }

      dispatch({ type: ACTIONS.SET_ERROR, error: null });
      dispatch({ type: ACTIONS.SET_LOADING, isLoading: true });
      dispatch({ type: ACTIONS.SET_TRANSITIONING, isTransitioning: true });

      // Update the database with current state of all questions
      // The answer for the current question is already saved by updateAnswer.
      // No need to save the entire questions array here again.
      // This block is removed to prevent duplicate API calls.

      const createVRResJson = Cookies.get("CreateVRres");
      const parsedData = JSON.parse(createVRResJson || localStorage.getItem("interviewData"));
      const skills = parsedData?.skills || ["JavaScript"];
      const role = parsedData?.jobRole || "Software Engineer";
      const companyType = parsedData?.companyType || "";
      const experience = parsedData?.experience || "";

      const MAX_RETRIES = 3;
      let retries = 0;
      let data = null;

      while (retries < MAX_RETRIES) {
        try {
          const nextQuestionResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-question`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              videoProfileId: vpid, // Pass videoProfileId
              role: role,
              skills: skills,
              companyType: companyType, // Pass companyType
              experience: experience, // Pass experience
              totalQuestions: 5,
              currentQuestionNumber: state.currentIndex + 2,
              previousQuestions: allQuestionsRef.current.map((q, index) => {
                const isCurrentQuestion = index === state.currentIndex;
                return {
                  question: q.question,
                  // For the current question, use the answer from the ref, which holds the latest state
                  answer: isCurrentQuestion ? (allQuestionsRef.current[state.currentIndex]?.answer || '') : (q.answer || ''),
                  type: q.type,
                  question_number: q.questionNumber, // Map frontend's questionNumber to backend's question_number
                  total_questions: 5 // Hardcode totalQuestions as 5, as per frontend's API call
                };
              })
            })
          });

          if (!nextQuestionResponse.ok) {
            throw new Error(`Failed to get next question: ${nextQuestionResponse.statusText}`);
          }

          data = await nextQuestionResponse.json();
          console.log("Frontend received data from API:", data);

          // 🤖 CHECK FOR AUTOMATIC TERMINATION
          if (data.completed && data.autoTerminated) {
            console.log("🤖 AUTOMATIC INTERVIEW TERMINATION DETECTED:", {
              reason: data.terminationReason,
              type: data.terminationType,
              finalStats: data.finalStats
            });

            // Show detailed termination message to user
            const terminationMessage = `Interview completed automatically: ${data.terminationReason}`;
            toast.success(terminationMessage, {
              duration: 6000,
              style: {
                background: '#10B981',
                color: 'white',
                fontWeight: 'bold'
              }
            });

            // Log final statistics
            if (data.finalStats) {
              console.log("📊 Final Interview Statistics:", data.finalStats);
            }

            // Set loading states to false immediately
            dispatch({ type: ACTIONS.SET_LOADING, isLoading: false });
            dispatch({ type: ACTIONS.SET_TRANSITIONING, isTransitioning: false });

            // Trigger interview completion immediately without delay
            // Use setTimeout with minimal delay to ensure state updates are processed
            setTimeout(() => {
              if (typeof onEndInterview === 'function') {
                console.log("🤖 Triggering automatic interview completion");
                // Pass the current questions array from state
                onEndInterview(state.allQuestions);
              } else {
                console.error("🤖 onEndInterview function not available");
              }
            }, 100); // Minimal delay to ensure state consistency

            // Don't return false immediately - let the function complete normally
            // but mark that termination has been triggered
            data._autoTerminationTriggered = true;
          }

          // Continue with normal question processing
          if (data.question && data.type) {
            break; // Valid question with type received, exit loop
          } else {
            console.warn(`API returned question without type, retrying... (Attempt ${retries + 1})`);
            retries++;
          }
        } catch (error) {
          console.error("Error fetching next question:", error);
          retries++;
        }
      }

      toast.dismiss();

      if (!data || !data.question || !data.type) {
        toast.error("Failed to generate a valid question after multiple retries. Please try again.");
        dispatch({ type: ACTIONS.SET_LOADING, isLoading: false });
        dispatch({ type: ACTIONS.SET_TRANSITIONING, isTransitioning: false });
        return false;
      }

      const nextQuestionData = {
        questionId: state.allQuestions.length + 1, // Assign a unique ID for the new question
        question: data.question,
        type: data.type,
        timerDuration: data.timerDuration || 90, // Add timer duration with fallback
        startTimestamp: null,
        endTimestamp: null,
        status: 'pending',
        isComplete: false,
      };

      console.log('Moving to next question:', {
        currentAnswer: state.currentQuestion?.answer,
        nextQuestion: nextQuestionData.question,
        nextQuestionType: nextQuestionData.type,
        questionCount: state.allQuestions.length + 1
      });

      try {
        // Create updated questions array with proper synchronization
        const updatedQuestions = [...state.allQuestions];
        if (state.currentQuestion?.answer !== undefined) {
          const finalizedCurrentQuestion = {
            ...state.currentQuestion,
            endTimestamp: new Date().toISOString(),
            status: 'completed',
            isComplete: true,
            answer: state.currentQuestion.answer || '', // Use empty string instead of null
            lastModified: new Date().toISOString(),
          };

          console.log('Finalizing current question:', {
            questionNumber: state.currentIndex + 1,
            answer: finalizedCurrentQuestion.answer,
            status: finalizedCurrentQuestion.status
          });
          updatedQuestions[state.currentIndex] = finalizedCurrentQuestion;
        }

        // Validate next question data
        if (!nextQuestionData.question) {
          throw new Error("Invalid next question data");
        }

        // Check for duplicates before adding
        const isDuplicate = updatedQuestions.some(q =>
          q.question === nextQuestionData.question);
        
        if (!isDuplicate) {
          updatedQuestions.push(nextQuestionData);
          
          dispatch({
            type: ACTIONS.NEXT_QUESTION,
            payload: {
              updatedQuestions,
              nextQuestionData
            }
          });
        } else {
          console.warn("Prevented duplicate question:", nextQuestionData.question);
          toast.error("Duplicate question detected, generating new question...");
          // Trigger a new question generation
          return nextQuestion();
        }
        
        return false;
      } catch (error) {
        console.error("Error in question transition:", error);
        toast.error("Error during question transition");
        return false;
      }

    } catch (error) {
      console.error("Error getting next question:", error);
      toast.error("Failed to get next question");
      return false;
    }
  };

  const calculateScores = (answer, question) => {
    const safeAnswer = typeof answer === 'string' ? answer.trim() : (answer ? String(answer).trim() : '');
    if (!safeAnswer || safeAnswer === '-') {
      return {
        communication: 1,
        technical: 1,
        overall: 1
      };
    }

    let communicationScore = 3;
    const wordCount = (typeof answer === 'string' ? answer.trim() : (answer ? String(answer).trim() : '')).split(/\s+/).length;
    
    if (wordCount < 5) {
      communicationScore = 1.5;
    } else if (wordCount < 15) {
      communicationScore = 2.5;
    } else if (wordCount > 50) {
      communicationScore = 4.5;
    }

    let technicalScore = 3;
    
    const hasSpecificExamples = answer.toLowerCase().includes('example') ||
                               answer.toLowerCase().includes('project') ||
                               answer.includes('.');
    const hasDetailedExplanation = wordCount > 30 && answer.includes(',');
    const hasTechnicalTerms = answer.match(/\b(function|api|database|code|test|debug|deploy|develop)\b/i);
    
    if (hasSpecificExamples && hasDetailedExplanation && hasTechnicalTerms) {
      technicalScore = 5;
    } else if ((hasSpecificExamples && hasDetailedExplanation) ||
               (hasDetailedExplanation && hasTechnicalTerms) ||
               (hasSpecificExamples && hasTechnicalTerms)) {
      technicalScore = 4;
    } else if (hasSpecificExamples || hasDetailedExplanation || hasTechnicalTerms) {
      technicalScore = 3.5;
    } else if (wordCount < 10) {
      technicalScore = 2;
    }

    const overallScore = (communicationScore * 0.4 + technicalScore * 0.6);

    return {
      communication: Number(communicationScore.toFixed(1)),
      technical: Number(technicalScore.toFixed(1)),
      overall: Number(overallScore.toFixed(1))
    };
  };

  // Prevent double backend save for last question
  const lastAnswerSavedRef = useRef({});

  const updateAnswer = useCallback(async (answer) => {
    if (!state.currentQuestion || state.currentIndex < 0) {
      console.warn('Invalid state for updating answer');
      return false;
    }

    // Safely handle non-string answers before trimming
    let safeAnswer = '';
    if (typeof answer === 'string') {
      safeAnswer = answer.trim();
    } else if (answer === undefined || answer === null) {
      safeAnswer = '';
    } else {
      // If answer is not a string, undefined, or null, convert to string
      safeAnswer = String(answer).trim();
    }

    // Check for valid answer text
    if (!safeAnswer) {
      return false;
    }

    // Prevent duplicate backend save for last question
    const isLastQuestion = state.currentIndex === state.allQuestions.length - 1;
    if (isLastQuestion && lastAnswerSavedRef.current[state.currentIndex]) {
      return true;
    }

    console.log('Updating answer for question:', {
      questionNumber: state.currentIndex + 1,
      questionType: state.currentQuestion.type,
      answerLength: safeAnswer.length
    });

    try {
      dispatch({ type: ACTIONS.SET_TRANSCRIBING, isTranscribing: true });

      // Update local state first
      dispatch({
        type: ACTIONS.UPDATE_ANSWER,
        payload: safeAnswer
      });
      
      // Try to sync with backend if we have vpid
      if (vpid && state.currentQuestion) {
        try {
          const payloadBody = {
            answer: typeof answer === 'string' ? answer.trim() : (answer ? String(answer).trim() : ''),
            startTimestamp: state.currentQuestion.startTimestamp,
            endTimestamp: new Date().toISOString(),
            status: 'answered',
            isComplete: true,
            lastModified: new Date().toISOString(),
          };

          let payload;
          const commonPayload = {
            question: state.currentQuestion.question, // Add question text
            type: state.currentQuestion.type, // Add question type
            ...payloadBody,
          };

          if (state.currentQuestion.type === 'coding') { // Assuming 'technical' is coding question
            payload = {
              updatedQuestion: commonPayload,
            };
          } else { // For behavioral/verbal questions
            payload = {
              updatedQuestion: commonPayload,
            };
          }
          console.log('Sending payload to backend:', payload); // Added log

          const saveResponse = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/videoprofile/${vpid}/questions`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          });

          if (!saveResponse.ok) {
            throw new Error(`Failed to save answer: ${saveResponse.statusText}`);
          }
          console.log("Answer saved successfully to backend.");
        } catch (error) {
          console.error("Error saving answer to backend:", error);
          toast.error("Failed to save answer. Please check your connection.");
        }
      }

      // Save last successful answer
      dispatch({ type: ACTIONS.SET_LAST_SAVED_ANSWER, answer: typeof answer === 'string' ? answer.trim() : (answer ? String(answer).trim() : '') });
      if (isLastQuestion) {
        lastAnswerSavedRef.current[state.currentIndex] = true;
      }
      return true;

    } catch (error) {
      console.error("Error updating answer:", error);
      toast.error("Failed to save answer. Please try again.");
      return false;
    } finally {
      dispatch({ type: ACTIONS.SET_TRANSCRIBING, isTranscribing: false });
    }
  }, [state.currentQuestion, state.currentIndex, state.allQuestions.length]);

  const startAnswering = () => {
    if (!state.currentQuestion) {
      console.warn("Cannot start answering - no current question");
      toast.error("Please wait for question to load");
      return;
    }

    if (state.isTranscribing) {
      console.warn("Already transcribing an answer");
      toast.error("Please wait for current answer to complete");
      return;
    }

    try {
      cleanup(); // Clean up any existing audio/timers

      // Start the answer timer
      const timer = setTimeout(() => {
        console.log("Answer time limit reached");
        if (!state.currentQuestion.answer) {
          toast("Time's up - moving to next question", { icon: '⏰' });
          nextQuestion();
        }
      }, ANSWER_TIME_LIMIT);

      // Update timer state
      dispatch({
        type: ACTIONS.SET_ANSWER_TIMER,
        timer
      });

      if (!state.currentQuestion.startTimestamp) {
        // First update the metadata
        dispatch({
          type: ACTIONS.SET_QUESTION_META,
          payload: {
            startTimestamp: new Date().toISOString(),
            status: 'recording',
            isComplete: false
          }
        });
        // toast.success("Recording started", { icon: '🎤' });
      }
    } catch (error) {
      console.error("Error in startAnswering:", error);
      toast.error("Failed to start answering");
    }
  };

  const activateQuestions = useCallback(() => {
    if (!state.questionsLoaded) {
      console.error('Cannot activate questions - questionsLoaded is false');
      toast.error('Questions not loaded yet. Please wait and try again.');
      return false;
    }

    if (state.allQuestions.length === 0) {
      console.error('Cannot activate questions - no questions in array');
      toast.error('No questions available. Please refresh and try again.');
      return false;
    }

    const firstQuestion = {
      ...state.allQuestions[0],
      startTimestamp: new Date().toISOString()
    };

    const updatedQuestions = state.allQuestions.map((q, index) => 
      index === 0 ? firstQuestion : q
    );

    dispatch({
      type: ACTIONS.SET_QUESTIONS,
      questions: updatedQuestions,
      index: 0
    });

    toast('Interview started - tell us about your experience', { icon: '🎬' });
    return true;
  }, [state.questionsLoaded, state.allQuestions]);

  const initializeQuestions = async (initialQuestions) => {
    try {
      if (!Array.isArray(initialQuestions) || initialQuestions.length === 0) {
        throw new Error('Invalid or empty questions array');
      }

     const normalizedQuestions = initialQuestions.map((q, index) => ({
       ...q,
       question: q.question || (typeof q === 'string' ? q : '') || "What is your experience with [skill]?",
       timerDuration: q.timerDuration || 90, // Add timer duration with fallback
       startTimestamp: null,
       endTimestamp: null,
       answer: q.answer || '', // Use empty string instead of null
       follow_up: q.follow_up || '',
       type: q.type || 'technical',
       questionNumber: index + 1,
       status: 'pending',
       isComplete: false,
       scores: {
         communication: 0,
         technical: 0,
         overall: 0
       }
     }));

     // Log questions for debugging
     console.log('Initialized questions:', normalizedQuestions.map(q => ({
       question: q.question,
       answer: q.answer,
       status: q.status
     })));

      dispatch({
        type: ACTIONS.SET_QUESTIONS,
        questions: normalizedQuestions,
        index: 0
      });
      dispatch({ type: ACTIONS.SET_QUESTIONS_LOADED, loaded: true });
      dispatch({ type: ACTIONS.SET_ERROR, error: null });

      return true;
    } catch (error) {
      console.error("Error initializing questions:", error);
      dispatch({ type: ACTIONS.SET_ERROR, error: "Failed to initialize questions" });
      dispatch({ type: ACTIONS.SET_QUESTIONS_LOADED, loaded: false });
      return false;
    } finally {
      dispatch({ type: ACTIONS.SET_LOADING, isLoading: false });
    }
  };

  return {
    questions: state.allQuestions,
    currentQuestion: state.currentQuestion,
    currentIndex: state.currentIndex,
    nextQuestion,
    startAnswering,
    updateAnswer,
    initializeQuestions,
    loadQuestionsData,
    activateQuestions,
    questionsLoaded: state.questionsLoaded,
    isTransitioning: state.isTransitioning,
    error: state.error,
    isLoading: state.isLoading,
    hasNarrated: state.hasNarrated,
    isTimeUp: state.isTimeUp,
    cleanup,
    ANSWER_TIME_LIMIT,
    setHasNarrated: (value) => dispatch({
      type: ACTIONS.SET_HAS_NARRATED,
      hasNarrated: value 
    })
  };
}
